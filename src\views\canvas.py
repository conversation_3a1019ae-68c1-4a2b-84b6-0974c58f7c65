"""
思维导图画布组件
"""
import wx
from typing import Optional, Tuple
from ..models.node import Node
from ..utils.constants import *
from ..utils.helpers import get_connection_points, create_default_font


class MindMapCanvas(wx.Panel):
    """思维导图画布类"""
    
    def __init__(self, parent, controller):
        super().__init__(parent)
        self.controller = controller
        self.controller.add_observer(self)
        
        # 画布状态
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        
        # 交互状态
        self.dragging_node = None
        self.drag_start_pos = None
        self.last_mouse_pos = None
        self.is_panning = False
        
        # 字体
        self.font = create_default_font(NODE_FONT_SIZE)
        
        # 设置背景色
        self.SetBackgroundColour(CANVAS_BG_COLOR)
        
        # 绑定事件
        self.Bind(wx.EVT_PAINT, self.on_paint)
        self.Bind(wx.EVT_SIZE, self.on_size)
        self.Bind(wx.EVT_LEFT_DOWN, self.on_left_down)
        self.Bind(wx.EVT_LEFT_UP, self.on_left_up)
        self.Bind(wx.EVT_RIGHT_DOWN, self.on_right_down)
        self.Bind(wx.EVT_MOTION, self.on_mouse_move)
        self.Bind(wx.EVT_MOUSEWHEEL, self.on_mouse_wheel)
        self.Bind(wx.EVT_KEY_DOWN, self.on_key_down)
        
        # 设置可以接收键盘焦点
        self.SetCanFocus(True)
    
    def on_paint(self, event):
        """绘制事件"""
        dc = wx.PaintDC(self)
        self.draw(dc)
    
    def draw(self, dc):
        """绘制思维导图"""
        # 清空画布
        dc.Clear()
        
        # 设置变换
        dc.SetUserScale(self.zoom_factor, self.zoom_factor)
        dc.SetDeviceOrigin(self.pan_x, self.pan_y)
        
        # 绘制连接线
        self.draw_connections(dc)
        
        # 绘制节点
        self.draw_nodes(dc)
    
    def draw_connections(self, dc):
        """绘制节点之间的连接线"""
        dc.SetPen(wx.Pen(LINE_COLOR, 2))
        
        for node in self.controller.get_all_nodes():
            children = self.controller.mindmap.get_children(node.id)
            for child in children:
                p1, p2 = get_connection_points(node, child)
                dc.DrawLine(int(p1[0]), int(p1[1]), int(p2[0]), int(p2[1]))
    
    def draw_nodes(self, dc):
        """绘制所有节点"""
        dc.SetFont(self.font)
        
        for node in self.controller.get_all_nodes():
            self.draw_node(dc, node)
    
    def draw_node(self, dc, node: Node):
        """绘制单个节点"""
        x, y, w, h = int(node.x), int(node.y), int(node.width), int(node.height)
        
        # 选择颜色
        if node.is_selected:
            bg_color = NODE_SELECTED_COLOR
            border_color = NODE_SELECTED_COLOR
        else:
            bg_color = NODE_BG_COLOR
            border_color = NODE_BORDER_COLOR
        
        # 绘制节点背景
        dc.SetBrush(wx.Brush(bg_color))
        dc.SetPen(wx.Pen(border_color, NODE_BORDER_WIDTH))
        dc.DrawRoundedRectangle(x, y, w, h, 5)
        
        # 绘制文本
        dc.SetTextForeground(NODE_TEXT_COLOR)
        text_w, text_h = dc.GetTextExtent(node.text)
        text_x = x + (w - text_w) // 2
        text_y = y + (h - text_h) // 2
        dc.DrawText(node.text, text_x, text_y)
    
    def screen_to_world(self, screen_x: int, screen_y: int) -> Tuple[float, float]:
        """屏幕坐标转换为世界坐标"""
        world_x = (screen_x - self.pan_x) / self.zoom_factor
        world_y = (screen_y - self.pan_y) / self.zoom_factor
        return world_x, world_y
    
    def world_to_screen(self, world_x: float, world_y: float) -> Tuple[int, int]:
        """世界坐标转换为屏幕坐标"""
        screen_x = int(world_x * self.zoom_factor + self.pan_x)
        screen_y = int(world_y * self.zoom_factor + self.pan_y)
        return screen_x, screen_y
    
    def on_left_down(self, event):
        """鼠标左键按下"""
        self.SetFocus()  # 获取键盘焦点
        
        pos = event.GetPosition()
        world_pos = self.screen_to_world(pos.x, pos.y)
        
        # 查找点击的节点
        clicked_node = self.controller.get_node_at_position(world_pos[0], world_pos[1])
        
        if clicked_node:
            # 选择节点
            self.controller.select_node(clicked_node.id)
            
            # 开始拖拽
            self.dragging_node = clicked_node
            self.drag_start_pos = world_pos
        else:
            # 清除选择
            self.controller.select_node(None)
            
            # 开始平移
            self.is_panning = True
        
        self.last_mouse_pos = pos
        self.CaptureMouse()
    
    def on_left_up(self, event):
        """鼠标左键释放"""
        if self.HasCapture():
            self.ReleaseMouse()
        
        self.dragging_node = None
        self.drag_start_pos = None
        self.is_panning = False
    
    def on_right_down(self, event):
        """鼠标右键按下 - 显示上下文菜单"""
        pos = event.GetPosition()
        world_pos = self.screen_to_world(pos.x, pos.y)
        
        clicked_node = self.controller.get_node_at_position(world_pos[0], world_pos[1])
        
        if clicked_node:
            self.controller.select_node(clicked_node.id)
            self.show_node_context_menu(pos)
        else:
            self.show_canvas_context_menu(pos)
    
    def on_mouse_move(self, event):
        """鼠标移动"""
        if not self.last_mouse_pos:
            return
        
        pos = event.GetPosition()
        dx = pos.x - self.last_mouse_pos.x
        dy = pos.y - self.last_mouse_pos.y
        
        if self.dragging_node and self.drag_start_pos:
            # 拖拽节点
            world_pos = self.screen_to_world(pos.x, pos.y)
            self.controller.move_node(self.dragging_node.id, world_pos[0], world_pos[1])
        elif self.is_panning:
            # 平移画布
            self.pan_x += dx
            self.pan_y += dy
            self.Refresh()
        
        self.last_mouse_pos = pos
    
    def on_mouse_wheel(self, event):
        """鼠标滚轮 - 缩放"""
        rotation = event.GetWheelRotation()
        pos = event.GetPosition()
        
        # 计算缩放中心
        world_pos = self.screen_to_world(pos.x, pos.y)
        
        # 计算新的缩放因子
        if rotation > 0:
            new_zoom = self.zoom_factor * ZOOM_FACTOR
        else:
            new_zoom = self.zoom_factor / ZOOM_FACTOR
        
        new_zoom = max(MIN_ZOOM, min(MAX_ZOOM, new_zoom))
        
        # 调整平移以保持缩放中心不变
        zoom_ratio = new_zoom / self.zoom_factor
        self.pan_x = pos.x - (pos.x - self.pan_x) * zoom_ratio
        self.pan_y = pos.y - (pos.y - self.pan_y) * zoom_ratio
        
        self.zoom_factor = new_zoom
        self.Refresh()
    
    def on_key_down(self, event):
        """键盘按下"""
        key_code = event.GetKeyCode()
        
        if key_code == wx.WXK_DELETE:
            # 删除选中的节点
            selected_node = self.controller.get_selected_node()
            if selected_node and not selected_node.is_root:
                self.controller.remove_node(selected_node.id)
        elif key_code == wx.WXK_F2:
            # 编辑选中节点的文本
            self.edit_selected_node()
        elif key_code == wx.WXK_TAB:
            # 添加子节点
            selected_node = self.controller.get_selected_node()
            if selected_node:
                self.controller.add_child_node(selected_node.id)
        
        event.Skip()
    
    def on_size(self, event):
        """窗口大小改变"""
        self.Refresh()
        event.Skip()
    
    def show_node_context_menu(self, pos):
        """显示节点上下文菜单"""
        menu = wx.Menu()
        
        edit_item = menu.Append(wx.ID_ANY, "编辑文本")
        add_child_item = menu.Append(wx.ID_ANY, "添加子节点")
        menu.AppendSeparator()
        delete_item = menu.Append(wx.ID_ANY, "删除节点")
        
        # 绑定菜单事件
        self.Bind(wx.EVT_MENU, lambda e: self.edit_selected_node(), edit_item)
        self.Bind(wx.EVT_MENU, lambda e: self.add_child_to_selected(), add_child_item)
        self.Bind(wx.EVT_MENU, lambda e: self.delete_selected_node(), delete_item)
        
        # 如果是根节点，禁用删除
        selected_node = self.controller.get_selected_node()
        if selected_node and selected_node.is_root:
            delete_item.Enable(False)
        
        self.PopupMenu(menu, pos)
        menu.Destroy()
    
    def show_canvas_context_menu(self, pos):
        """显示画布上下文菜单"""
        menu = wx.Menu()
        
        reset_view_item = menu.Append(wx.ID_ANY, "重置视图")
        
        self.Bind(wx.EVT_MENU, lambda e: self.reset_view(), reset_view_item)
        
        self.PopupMenu(menu, pos)
        menu.Destroy()
    
    def edit_selected_node(self):
        """编辑选中节点的文本"""
        selected_node = self.controller.get_selected_node()
        if not selected_node:
            return
        
        dlg = wx.TextEntryDialog(self, "请输入节点文本:", "编辑节点", selected_node.text)
        if dlg.ShowModal() == wx.ID_OK:
            new_text = dlg.GetValue().strip()
            if new_text:
                self.controller.update_node_text(selected_node.id, new_text)
        dlg.Destroy()
    
    def add_child_to_selected(self):
        """为选中节点添加子节点"""
        selected_node = self.controller.get_selected_node()
        if selected_node:
            self.controller.add_child_node(selected_node.id)
    
    def delete_selected_node(self):
        """删除选中的节点"""
        selected_node = self.controller.get_selected_node()
        if selected_node and not selected_node.is_root:
            self.controller.remove_node(selected_node.id)
    
    def reset_view(self):
        """重置视图"""
        self.zoom_factor = 1.0
        self.pan_x = 0
        self.pan_y = 0
        self.Refresh()
    
    def on_mindmap_changed(self, event_type: str, data):
        """响应思维导图变化"""
        self.Refresh()  # 重新绘制画布
