"""
思维导图节点数据模型
"""
import uuid
from typing import List, Optional, Dict, Any


class Node:
    """思维导图节点类"""

    def __init__(self, text: str = "新节点", x: float = 0, y: float = 0):
        """
        初始化节点

        Args:
            text: 节点文本内容
            x: 节点X坐标
            y: 节点Y坐标
        """
        self.id = str(uuid.uuid4())
        self.text = text
        self.x = x
        self.y = y
        self.width = 120
        self.height = 40
        self.parent_id: Optional[str] = None
        self.children_ids: List[str] = []
        self.is_selected = False
        self.is_expanded = True
        self.is_root = False

    def add_child(self, child_id: str) -> None:
        """添加子节点ID"""
        if child_id not in self.children_ids:
            self.children_ids.append(child_id)

    def remove_child(self, child_id: str) -> None:
        """移除子节点ID"""
        if child_id in self.children_ids:
            self.children_ids.remove(child_id)

    def set_parent(self, parent_id: Optional[str]) -> None:
        """设置父节点ID"""
        self.parent_id = parent_id

    def move_to(self, x: float, y: float) -> None:
        """移动节点到指定位置"""
        self.x = x
        self.y = y

    def get_bounds(self) -> tuple:
        """获取节点边界 (left, top, right, bottom)"""
        return (self.x, self.y, self.x + self.width, self.y + self.height)

    def contains_point(self, x: float, y: float) -> bool:
        """检查点是否在节点内"""
        left, top, right, bottom = self.get_bounds()
        return left <= x <= right and top <= y <= bottom

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'id': self.id,
            'text': self.text,
            'x': self.x,
            'y': self.y,
            'width': self.width,
            'height': self.height,
            'parent_id': self.parent_id,
            'children_ids': self.children_ids.copy(),
            'is_expanded': self.is_expanded,
            'is_root': self.is_root
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Node':
        """从字典创建节点"""
        node = cls(data.get('text', '新节点'), data.get('x', 0), data.get('y', 0))
        node.id = data.get('id', node.id)
        node.width = data.get('width', 120)
        node.height = data.get('height', 40)
        node.parent_id = data.get('parent_id')
        node.children_ids = data.get('children_ids', []).copy()
        node.is_expanded = data.get('is_expanded', True)
        node.is_root = data.get('is_root', False)
        return node

    def __str__(self) -> str:
        return f"Node(id={self.id[:8]}, text='{self.text}', pos=({self.x}, {self.y}))"
