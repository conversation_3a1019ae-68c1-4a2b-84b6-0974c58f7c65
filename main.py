#!/usr/bin/env python3
"""
思维导图程序主入口
"""
import wx
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.views.main_window import MainWindow


class MindMapApp(wx.App):
    """思维导图应用程序类"""
    
    def OnInit(self):
        """应用程序初始化"""
        # 创建主窗口
        frame = MainWindow()
        frame.Show()
        
        # 设置为主窗口
        self.SetTopWindow(frame)
        
        return True


def main():
    """主函数"""
    # 创建应用程序实例
    app = MindMapApp()
    
    # 运行主循环
    app.MainLoop()


if __name__ == '__main__':
    main()
