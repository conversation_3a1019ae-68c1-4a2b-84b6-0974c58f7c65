# 思维导图程序

一个基于Python和wxPython开发的轻量级思维导图程序，支持创建、编辑和管理思维导图。

## 功能特性

### 核心功能
- ✅ 创建、编辑和删除思维导图节点
- ✅ 支持节点的层级结构（父子关系）
- ✅ 节点可以添加和编辑文本内容
- ✅ 支持拖拽移动节点位置
- ✅ 支持缩放和平移画布
- ✅ 能够保存和加载思维导图数据（JSON格式）
- ✅ 导出为文本格式
- ✅ 提供简洁直观的用户界面

### 交互操作
- **鼠标左键点击**：选择节点
- **鼠标拖拽**：移动节点或平移画布
- **鼠标右键**：显示上下文菜单
- **鼠标滚轮**：缩放画布
- **Tab键**：为选中节点添加子节点
- **F2键**：编辑选中节点的文本
- **Delete键**：删除选中的节点

## 技术架构

### 项目结构
```
siweidaotu_1/
├── main.py                 # 主程序入口
├── src/
│   ├── models/             # 数据模型层
│   │   ├── node.py         # 节点数据模型
│   │   └── mindmap.py      # 思维导图数据模型
│   ├── views/              # 视图层
│   │   ├── main_window.py  # 主窗口
│   │   └── canvas.py       # 绘图画布
│   ├── controllers/        # 控制器层
│   │   ├── mindmap_controller.py  # 思维导图控制器
│   │   └── file_manager.py        # 文件管理器
│   └── utils/              # 工具模块
│       ├── constants.py    # 常量定义
│       └── helpers.py      # 辅助函数
├── data/
│   └── sample.json         # 示例数据文件
├── tests/                  # 测试用例
├── requirements.txt        # 依赖包列表
└── README.md              # 项目说明文档
```

### 技术栈
- **Python 3.10+**：主要编程语言
- **wxPython 4.2.0+**：GUI框架
- **JSON**：数据存储格式

### 设计模式
- **MVC架构**：模型-视图-控制器分离
- **观察者模式**：用于视图更新通知
- **单例模式**：文件管理器

## 安装和运行

### 环境要求
- Python 3.10 或更高版本
- wxPython 4.2.0 或更高版本

### 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <repository-url>
   cd siweidaotu_1
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **运行程序**
   ```bash
   python main.py
   ```

### 开发环境设置

如果需要进行开发，建议创建虚拟环境：

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 使用说明

### 基本操作

1. **创建新思维导图**
   - 启动程序后会自动创建一个包含"中心主题"的新思维导图
   - 或通过菜单"文件" → "新建"创建

2. **添加节点**
   - 选中一个节点后按Tab键
   - 或右键点击节点选择"添加子节点"
   - 或通过菜单"编辑" → "添加子节点"

3. **编辑节点文本**
   - 双击节点或按F2键
   - 或右键点击节点选择"编辑文本"

4. **移动节点**
   - 拖拽节点到新位置

5. **删除节点**
   - 选中节点后按Delete键
   - 或右键点击节点选择"删除节点"
   - 注意：根节点不能删除

6. **画布操作**
   - 鼠标滚轮缩放
   - 拖拽空白区域平移画布
   - 通过菜单"视图"重置视图

### 文件操作

1. **保存思维导图**
   - Ctrl+S 或菜单"文件" → "保存"
   - 首次保存会提示选择保存位置

2. **打开思维导图**
   - Ctrl+O 或菜单"文件" → "打开"
   - 支持.json格式文件

3. **导出为文本**
   - 菜单"文件" → "导出为文本"
   - 生成层级结构的文本文件

### 快捷键

| 快捷键 | 功能 |
|--------|------|
| Ctrl+N | 新建思维导图 |
| Ctrl+O | 打开文件 |
| Ctrl+S | 保存文件 |
| Ctrl+Shift+S | 另存为 |
| Tab | 添加子节点 |
| F2 | 编辑节点文本 |
| Delete | 删除节点 |
| Ctrl++ | 放大视图 |
| Ctrl+- | 缩小视图 |
| Ctrl+0 | 重置缩放 |

## 测试

项目包含完整的单元测试，覆盖模型、控制器和工具函数。

### 运行测试

```bash
# 运行所有测试
python -m unittest discover tests

# 运行特定测试文件
python -m unittest tests.test_models
python -m unittest tests.test_controllers
python -m unittest tests.test_utils

# 运行特定测试类
python -m unittest tests.test_models.TestNode
```

### 测试覆盖

- **模型测试**：节点和思维导图的数据操作
- **控制器测试**：业务逻辑和文件管理
- **工具函数测试**：辅助函数和常量定义

## 开发指南

### 代码结构

项目采用MVC架构：

- **Model（模型）**：`src/models/` - 数据结构和业务逻辑
- **View（视图）**：`src/views/` - 用户界面组件
- **Controller（控制器）**：`src/controllers/` - 协调模型和视图

### 扩展功能

如需添加新功能，建议遵循以下步骤：

1. 在相应的模型中添加数据结构
2. 在控制器中实现业务逻辑
3. 在视图中添加用户界面
4. 编写相应的测试用例

### 代码规范

- 使用Python PEP 8编码规范
- 添加适当的文档字符串
- 保持函数和类的单一职责
- 编写测试用例确保代码质量

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基本的思维导图功能
- 支持节点的创建、编辑、删除
- 支持文件的保存和加载
- 提供完整的测试用例
