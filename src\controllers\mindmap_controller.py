"""
思维导图控制器 - 协调模型和视图之间的交互
"""
from typing import Optional, Tuple, List
from ..models.mindmap import MindMap
from ..models.node import Node
from .file_manager import FileManager


class MindMapController:
    """思维导图控制器类"""
    
    def __init__(self):
        self.mindmap = MindMap()
        self.file_manager = FileManager()
        self.observers = []  # 观察者列表，用于通知视图更新
        
        # 创建默认根节点
        self.create_root_node()
    
    def add_observer(self, observer) -> None:
        """添加观察者"""
        if observer not in self.observers:
            self.observers.append(observer)
    
    def remove_observer(self, observer) -> None:
        """移除观察者"""
        if observer in self.observers:
            self.observers.remove(observer)
    
    def notify_observers(self, event_type: str, data=None) -> None:
        """通知所有观察者"""
        for observer in self.observers:
            if hasattr(observer, 'on_mindmap_changed'):
                observer.on_mindmap_changed(event_type, data)
    
    def create_root_node(self) -> str:
        """创建根节点"""
        root_node = Node("中心主题", 400, 300)
        root_node.is_root = True
        node_id = self.mindmap.add_node(root_node)
        self.file_manager.set_modified(True)
        self.notify_observers('node_added', root_node)
        return node_id
    
    def add_child_node(self, parent_id: str, text: str = "新节点", 
                      x: float = 0, y: float = 0) -> Optional[str]:
        """
        添加子节点
        
        Args:
            parent_id: 父节点ID
            text: 节点文本
            x: X坐标
            y: Y坐标
            
        Returns:
            新节点的ID，失败时返回None
        """
        parent_node = self.mindmap.get_node(parent_id)
        if not parent_node:
            return None
        
        # 如果没有指定位置，自动计算位置
        if x == 0 and y == 0:
            x, y = self._calculate_child_position(parent_node)
        
        new_node = Node(text, x, y)
        node_id = self.mindmap.add_node(new_node, parent_id)
        self.file_manager.set_modified(True)
        self.notify_observers('node_added', new_node)
        return node_id
    
    def remove_node(self, node_id: str) -> bool:
        """删除节点"""
        node = self.mindmap.get_node(node_id)
        if not node or node.is_root:  # 不能删除根节点
            return False
        
        success = self.mindmap.remove_node(node_id)
        if success:
            self.file_manager.set_modified(True)
            self.notify_observers('node_removed', node_id)
        return success
    
    def update_node_text(self, node_id: str, text: str) -> bool:
        """更新节点文本"""
        node = self.mindmap.get_node(node_id)
        if not node:
            return False
        
        node.text = text
        self.file_manager.set_modified(True)
        self.notify_observers('node_updated', node)
        return True
    
    def move_node(self, node_id: str, x: float, y: float) -> bool:
        """移动节点"""
        node = self.mindmap.get_node(node_id)
        if not node:
            return False
        
        node.move_to(x, y)
        self.file_manager.set_modified(True)
        self.notify_observers('node_moved', node)
        return True
    
    def select_node(self, node_id: Optional[str]) -> None:
        """选择节点"""
        self.mindmap.select_node(node_id)
        self.notify_observers('node_selected', node_id)
    
    def get_node_at_position(self, x: float, y: float) -> Optional[Node]:
        """获取指定位置的节点"""
        for node in self.mindmap.get_all_nodes():
            if node.contains_point(x, y):
                return node
        return None
    
    def get_all_nodes(self) -> List[Node]:
        """获取所有节点"""
        return self.mindmap.get_all_nodes()
    
    def get_selected_node(self) -> Optional[Node]:
        """获取当前选中的节点"""
        return self.mindmap.get_selected_node()
    
    def new_mindmap(self) -> None:
        """创建新思维导图"""
        self.mindmap = self.file_manager.create_new_mindmap()
        self.create_root_node()
        self.notify_observers('mindmap_cleared', None)
    
    def save_mindmap(self, file_path: str) -> bool:
        """保存思维导图"""
        success = self.file_manager.save_mindmap(self.mindmap, file_path)
        if success:
            self.notify_observers('mindmap_saved', file_path)
        return success
    
    def load_mindmap(self, file_path: str) -> bool:
        """加载思维导图"""
        mindmap = self.file_manager.load_mindmap(file_path)
        if mindmap:
            self.mindmap = mindmap
            self.notify_observers('mindmap_loaded', file_path)
            return True
        return False
    
    def is_modified(self) -> bool:
        """检查是否有未保存的修改"""
        return self.file_manager.is_file_modified()
    
    def get_current_file_name(self) -> str:
        """获取当前文件名"""
        return self.file_manager.get_current_file_name()
    
    def _calculate_child_position(self, parent_node: Node) -> Tuple[float, float]:
        """计算子节点的默认位置"""
        children = self.mindmap.get_children(parent_node.id)
        child_count = len(children)
        
        # 基础偏移
        offset_x = 200
        offset_y = 80
        
        # 根据子节点数量调整位置
        if child_count == 0:
            # 第一个子节点放在右侧
            x = parent_node.x + offset_x
            y = parent_node.y
        else:
            # 后续子节点垂直排列
            x = parent_node.x + offset_x
            y = parent_node.y + (child_count * offset_y)
        
        return x, y
