"""
辅助函数
"""
import math
import wx
from typing import Tu<PERSON>


def distance(x1: float, y1: float, x2: float, y2: float) -> float:
    """计算两点之间的距离"""
    return math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2)


def clamp(value: float, min_value: float, max_value: float) -> float:
    """将值限制在指定范围内"""
    return max(min_value, min(value, max_value))


def get_text_size(text: str, font: wx.Font, dc: wx.DC) -> Tuple[int, int]:
    """获取文本在指定字体下的尺寸"""
    dc.Set<PERSON>ont(font)
    return dc.GetTextExtent(text)


def auto_resize_node(node, text: str, font: wx.Font, dc: wx.DC, 
                    min_width: int = 80, min_height: int = 30, 
                    padding: int = 20) -> None:
    """根据文本内容自动调整节点大小"""
    text_width, text_height = get_text_size(text, font, dc)
    node.width = max(min_width, text_width + padding)
    node.height = max(min_height, text_height + padding)


def point_in_rect(px: float, py: float, x: float, y: float, 
                 width: float, height: float) -> bool:
    """检查点是否在矩形内"""
    return x <= px <= x + width and y <= py <= y + height


def rect_intersect(x1: float, y1: float, w1: float, h1: float,
                  x2: float, y2: float, w2: float, h2: float) -> bool:
    """检查两个矩形是否相交"""
    return not (x1 + w1 < x2 or x2 + w2 < x1 or y1 + h1 < y2 or y2 + h2 < y1)


def get_connection_points(node1, node2) -> Tuple[Tuple[float, float], Tuple[float, float]]:
    """获取两个节点之间的连接点"""
    # 计算节点中心点
    x1, y1 = node1.x + node1.width / 2, node1.y + node1.height / 2
    x2, y2 = node2.x + node2.width / 2, node2.y + node2.height / 2
    
    # 计算连接方向
    dx = x2 - x1
    dy = y2 - y1
    
    # 计算节点边界上的连接点
    if abs(dx) > abs(dy):
        # 水平连接
        if dx > 0:
            # 从左到右
            p1 = (node1.x + node1.width, y1)
            p2 = (node2.x, y2)
        else:
            # 从右到左
            p1 = (node1.x, y1)
            p2 = (node2.x + node2.width, y2)
    else:
        # 垂直连接
        if dy > 0:
            # 从上到下
            p1 = (x1, node1.y + node1.height)
            p2 = (x2, node2.y)
        else:
            # 从下到上
            p1 = (x1, node1.y)
            p2 = (x2, node2.y + node2.height)
    
    return p1, p2


def format_text_for_display(text: str, max_width: int, font: wx.Font, dc: wx.DC) -> str:
    """格式化文本以适应显示宽度"""
    if not text:
        return ""
    
    dc.SetFont(font)
    text_width, _ = dc.GetTextExtent(text)
    
    if text_width <= max_width:
        return text
    
    # 如果文本太长，添加省略号
    ellipsis = "..."
    ellipsis_width, _ = dc.GetTextExtent(ellipsis)
    
    # 二分查找合适的长度
    left, right = 0, len(text)
    result = text
    
    while left < right:
        mid = (left + right + 1) // 2
        test_text = text[:mid] + ellipsis
        test_width, _ = dc.GetTextExtent(test_text)
        
        if test_width <= max_width:
            result = test_text
            left = mid
        else:
            right = mid - 1
    
    return result


def create_default_font(size: int = 12) -> wx.Font:
    """创建默认字体"""
    return wx.Font(size, wx.FONTFAMILY_DEFAULT, wx.FONTSTYLE_NORMAL, wx.FONTWEIGHT_NORMAL)
