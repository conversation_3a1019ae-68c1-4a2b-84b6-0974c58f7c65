"""
主窗口
"""
import wx
import os
from ..controllers.mindmap_controller import MindMapController
from ..utils.constants import *
from .canvas import MindMapCanvas


class MainWindow(wx.Frame):
    """主窗口类"""

    def __init__(self):
        super().__init__(None, title=WINDOW_TITLE, size=(WINDOW_WIDTH, WINDOW_HEIGHT))

        # 创建控制器
        self.controller = MindMapController()
        self.controller.add_observer(self)

        # 创建界面
        self.create_menu_bar()
        self.create_toolbar()
        self.create_status_bar()
        self.create_main_panel()

        # 设置图标和居中显示
        self.Center()

        # 绑定关闭事件
        self.Bind(wx.EVT_CLOSE, self.on_close)

        # 更新标题
        self.update_title()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = wx.MenuBar()

        # 文件菜单
        file_menu = wx.Menu()

        new_item = file_menu.Append(wx.ID_NEW, "新建\tCtrl+N", "创建新的思维导图")
        open_item = file_menu.Append(wx.ID_OPEN, "打开\tCtrl+O", "打开思维导图文件")
        file_menu.AppendSeparator()
        save_item = file_menu.Append(wx.ID_SAVE, "保存\tCtrl+S", "保存当前思维导图")
        save_as_item = file_menu.Append(wx.ID_SAVEAS, "另存为\tCtrl+Shift+S", "另存为新文件")
        file_menu.AppendSeparator()
        export_item = file_menu.Append(wx.ID_ANY, "导出为文本", "导出为文本格式")
        file_menu.AppendSeparator()
        exit_item = file_menu.Append(wx.ID_EXIT, "退出\tCtrl+Q", "退出程序")

        # 编辑菜单
        edit_menu = wx.Menu()

        add_child_item = edit_menu.Append(wx.ID_ANY, "添加子节点\tTab", "为选中节点添加子节点")
        edit_text_item = edit_menu.Append(wx.ID_ANY, "编辑文本\tF2", "编辑选中节点的文本")
        edit_menu.AppendSeparator()
        delete_item = edit_menu.Append(wx.ID_DELETE, "删除节点\tDel", "删除选中的节点")

        # 视图菜单
        view_menu = wx.Menu()

        zoom_in_item = view_menu.Append(wx.ID_ANY, "放大\tCtrl++", "放大视图")
        zoom_out_item = view_menu.Append(wx.ID_ANY, "缩小\tCtrl+-", "缩小视图")
        zoom_reset_item = view_menu.Append(wx.ID_ANY, "重置缩放\tCtrl+0", "重置视图缩放")
        view_menu.AppendSeparator()
        reset_view_item = view_menu.Append(wx.ID_ANY, "重置视图", "重置视图位置和缩放")

        # 帮助菜单
        help_menu = wx.Menu()

        about_item = help_menu.Append(wx.ID_ABOUT, "关于", "关于思维导图程序")

        # 添加菜单到菜单栏
        menubar.Append(file_menu, "文件")
        menubar.Append(edit_menu, "编辑")
        menubar.Append(view_menu, "视图")
        menubar.Append(help_menu, "帮助")

        self.SetMenuBar(menubar)

        # 绑定菜单事件
        self.Bind(wx.EVT_MENU, self.on_new, new_item)
        self.Bind(wx.EVT_MENU, self.on_open, open_item)
        self.Bind(wx.EVT_MENU, self.on_save, save_item)
        self.Bind(wx.EVT_MENU, self.on_save_as, save_as_item)
        self.Bind(wx.EVT_MENU, self.on_export, export_item)
        self.Bind(wx.EVT_MENU, self.on_exit, exit_item)

        self.Bind(wx.EVT_MENU, self.on_add_child, add_child_item)
        self.Bind(wx.EVT_MENU, self.on_edit_text, edit_text_item)
        self.Bind(wx.EVT_MENU, self.on_delete, delete_item)

        self.Bind(wx.EVT_MENU, self.on_zoom_in, zoom_in_item)
        self.Bind(wx.EVT_MENU, self.on_zoom_out, zoom_out_item)
        self.Bind(wx.EVT_MENU, self.on_zoom_reset, zoom_reset_item)
        self.Bind(wx.EVT_MENU, self.on_reset_view, reset_view_item)

        self.Bind(wx.EVT_MENU, self.on_about, about_item)

    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.CreateToolBar()

        # 添加工具按钮
        toolbar.AddTool(wx.ID_NEW, "新建", wx.ArtProvider.GetBitmap(wx.ART_NEW), "新建思维导图")
        toolbar.AddTool(wx.ID_OPEN, "打开", wx.ArtProvider.GetBitmap(wx.ART_FILE_OPEN), "打开文件")
        toolbar.AddTool(wx.ID_SAVE, "保存", wx.ArtProvider.GetBitmap(wx.ART_FILE_SAVE), "保存文件")
        toolbar.AddSeparator()

        toolbar.AddTool(wx.ID_ANY, "添加", wx.ArtProvider.GetBitmap(wx.ART_PLUS), "添加子节点")
        toolbar.AddTool(wx.ID_DELETE, "删除", wx.ArtProvider.GetBitmap(wx.ART_DELETE), "删除节点")

        toolbar.Realize()

    def create_status_bar(self):
        """创建状态栏"""
        self.statusbar = self.CreateStatusBar()
        self.statusbar.SetStatusText("就绪")

    def create_main_panel(self):
        """创建主面板"""
        # 创建画布
        self.canvas = MindMapCanvas(self, self.controller)

        # 布局
        sizer = wx.BoxSizer(wx.VERTICAL)
        sizer.Add(self.canvas, 1, wx.EXPAND)
        self.SetSizer(sizer)

    # 文件菜单事件处理
    def on_new(self, event):
        """新建思维导图"""
        if self.check_save_changes():
            self.controller.new_mindmap()
            self.update_title()
            self.statusbar.SetStatusText("创建了新的思维导图")

    def on_open(self, event):
        """打开文件"""
        if not self.check_save_changes():
            return

        dlg = wx.FileDialog(self, "打开思维导图",
                           wildcard=FILE_WILDCARD,
                           style=wx.FD_OPEN | wx.FD_FILE_MUST_EXIST)

        if dlg.ShowModal() == wx.ID_OK:
            file_path = dlg.GetPath()
            if self.controller.load_mindmap(file_path):
                self.update_title()
                self.statusbar.SetStatusText(f"已打开: {os.path.basename(file_path)}")
            else:
                wx.MessageBox("无法打开文件", "错误", wx.OK | wx.ICON_ERROR)

        dlg.Destroy()

    def on_save(self, event):
        """保存文件"""
        current_path = self.controller.file_manager.get_current_file_path()
        if current_path:
            if self.controller.save_mindmap(current_path):
                self.update_title()
                self.statusbar.SetStatusText("文件已保存")
            else:
                wx.MessageBox("保存失败", "错误", wx.OK | wx.ICON_ERROR)
        else:
            self.on_save_as(event)

    def on_save_as(self, event):
        """另存为"""
        dlg = wx.FileDialog(self, "保存思维导图",
                           wildcard=FILE_WILDCARD,
                           style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT)

        if dlg.ShowModal() == wx.ID_OK:
            file_path = dlg.GetPath()
            if not file_path.endswith(DEFAULT_FILE_EXT):
                file_path += DEFAULT_FILE_EXT

            if self.controller.save_mindmap(file_path):
                self.update_title()
                self.statusbar.SetStatusText(f"已保存为: {os.path.basename(file_path)}")
            else:
                wx.MessageBox("保存失败", "错误", wx.OK | wx.ICON_ERROR)

        dlg.Destroy()

    def on_export(self, event):
        """导出为文本"""
        dlg = wx.FileDialog(self, "导出为文本",
                           wildcard="文本文件 (*.txt)|*.txt",
                           style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT)

        if dlg.ShowModal() == wx.ID_OK:
            file_path = dlg.GetPath()
            if not file_path.endswith('.txt'):
                file_path += '.txt'

            if self.controller.file_manager.export_to_text(self.controller.mindmap, file_path):
                self.statusbar.SetStatusText(f"已导出为: {os.path.basename(file_path)}")
            else:
                wx.MessageBox("导出失败", "错误", wx.OK | wx.ICON_ERROR)

        dlg.Destroy()

    def on_exit(self, event):
        """退出程序"""
        self.Close()

    # 编辑菜单事件处理
    def on_add_child(self, event):
        """添加子节点"""
        selected_node = self.controller.get_selected_node()
        if selected_node:
            self.controller.add_child_node(selected_node.id)
        else:
            wx.MessageBox("请先选择一个节点", "提示", wx.OK | wx.ICON_INFORMATION)

    def on_edit_text(self, event):
        """编辑文本"""
        self.canvas.edit_selected_node()

    def on_delete(self, event):
        """删除节点"""
        selected_node = self.controller.get_selected_node()
        if selected_node:
            if selected_node.is_root:
                wx.MessageBox("不能删除根节点", "提示", wx.OK | wx.ICON_INFORMATION)
            else:
                self.controller.remove_node(selected_node.id)
        else:
            wx.MessageBox("请先选择一个节点", "提示", wx.OK | wx.ICON_INFORMATION)

    # 视图菜单事件处理
    def on_zoom_in(self, event):
        """放大视图"""
        self.canvas.zoom_factor *= ZOOM_FACTOR
        self.canvas.zoom_factor = min(self.canvas.zoom_factor, MAX_ZOOM)
        self.canvas.Refresh()

    def on_zoom_out(self, event):
        """缩小视图"""
        self.canvas.zoom_factor /= ZOOM_FACTOR
        self.canvas.zoom_factor = max(self.canvas.zoom_factor, MIN_ZOOM)
        self.canvas.Refresh()

    def on_zoom_reset(self, event):
        """重置缩放"""
        self.canvas.zoom_factor = 1.0
        self.canvas.Refresh()

    def on_reset_view(self, event):
        """重置视图"""
        self.canvas.reset_view()

    # 帮助菜单事件处理
    def on_about(self, event):
        """关于对话框"""
        info = wx.adv.AboutDialogInfo()
        info.SetName("思维导图")
        info.SetVersion("1.0")
        info.SetDescription("一个轻量级的思维导图程序")
        info.SetCopyright("(C) 2024")
        info.AddDeveloper("开发者")

        wx.adv.AboutBox(info)

    # 窗口事件处理
    def on_close(self, event):
        """关闭窗口"""
        if self.check_save_changes():
            self.Destroy()
        else:
            event.Veto()

    # 辅助方法
    def check_save_changes(self) -> bool:
        """检查是否需要保存更改"""
        if self.controller.is_modified():
            dlg = wx.MessageDialog(self,
                                 "当前思维导图已修改，是否保存？",
                                 "保存确认",
                                 wx.YES_NO | wx.CANCEL | wx.ICON_QUESTION)

            result = dlg.ShowModal()
            dlg.Destroy()

            if result == wx.ID_YES:
                # 保存文件
                current_path = self.controller.file_manager.get_current_file_path()
                if current_path:
                    return self.controller.save_mindmap(current_path)
                else:
                    # 需要另存为
                    dlg = wx.FileDialog(self, "保存思维导图",
                                       wildcard=FILE_WILDCARD,
                                       style=wx.FD_SAVE | wx.FD_OVERWRITE_PROMPT)

                    if dlg.ShowModal() == wx.ID_OK:
                        file_path = dlg.GetPath()
                        if not file_path.endswith(DEFAULT_FILE_EXT):
                            file_path += DEFAULT_FILE_EXT
                        success = self.controller.save_mindmap(file_path)
                        dlg.Destroy()
                        return success
                    else:
                        dlg.Destroy()
                        return False
            elif result == wx.ID_NO:
                return True
            else:  # CANCEL
                return False

        return True

    def update_title(self):
        """更新窗口标题"""
        file_name = self.controller.get_current_file_name()
        modified_mark = "*" if self.controller.is_modified() else ""
        self.SetTitle(f"{file_name}{modified_mark} - {WINDOW_TITLE}")

    def on_mindmap_changed(self, event_type: str, data):
        """响应思维导图变化"""
        self.update_title()

        if event_type == 'mindmap_saved':
            self.statusbar.SetStatusText("文件已保存")
        elif event_type == 'mindmap_loaded':
            self.statusbar.SetStatusText(f"已加载: {os.path.basename(data)}")
        elif event_type == 'node_added':
            self.statusbar.SetStatusText("已添加节点")
        elif event_type == 'node_removed':
            self.statusbar.SetStatusText("已删除节点")
        elif event_type == 'node_updated':
            self.statusbar.SetStatusText("已更新节点")
        elif event_type == 'mindmap_cleared':
            self.statusbar.SetStatusText("已创建新思维导图")