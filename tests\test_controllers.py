"""
测试控制器
"""
import unittest
import tempfile
import os
import sys

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.controllers.mindmap_controller import MindMapController
from src.controllers.file_manager import FileManager
from src.models.mindmap import MindMap
from src.models.node import Node


class TestMindMapController(unittest.TestCase):
    """测试MindMapController类"""
    
    def setUp(self):
        """测试前准备"""
        self.controller = MindMapController()
    
    def test_controller_initialization(self):
        """测试控制器初始化"""
        # 应该自动创建根节点
        self.assertIsNotNone(self.controller.mindmap.root_id)
        root_node = self.controller.mindmap.get_root_node()
        self.assertIsNotNone(root_node)
        self.assertTrue(root_node.is_root)
        self.assertEqual(root_node.text, "中心主题")
    
    def test_add_child_node(self):
        """测试添加子节点"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 添加子节点
        child_id = self.controller.add_child_node(root_node.id, "新子节点")
        self.assertIsNotNone(child_id)
        
        # 验证子节点
        child_node = self.controller.mindmap.get_node(child_id)
        self.assertIsNotNone(child_node)
        self.assertEqual(child_node.text, "新子节点")
        self.assertEqual(child_node.parent_id, root_node.id)
        self.assertIn(child_id, root_node.children_ids)
    
    def test_remove_node(self):
        """测试删除节点"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 添加子节点
        child_id = self.controller.add_child_node(root_node.id, "待删除节点")
        
        # 删除子节点
        success = self.controller.remove_node(child_id)
        self.assertTrue(success)
        
        # 验证删除
        self.assertIsNone(self.controller.mindmap.get_node(child_id))
        self.assertNotIn(child_id, root_node.children_ids)
        
        # 不能删除根节点
        success = self.controller.remove_node(root_node.id)
        self.assertFalse(success)
    
    def test_update_node_text(self):
        """测试更新节点文本"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 更新文本
        new_text = "更新后的文本"
        success = self.controller.update_node_text(root_node.id, new_text)
        self.assertTrue(success)
        self.assertEqual(root_node.text, new_text)
    
    def test_move_node(self):
        """测试移动节点"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 移动节点
        new_x, new_y = 500, 400
        success = self.controller.move_node(root_node.id, new_x, new_y)
        self.assertTrue(success)
        self.assertEqual(root_node.x, new_x)
        self.assertEqual(root_node.y, new_y)
    
    def test_select_node(self):
        """测试选择节点"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 选择节点
        self.controller.select_node(root_node.id)
        selected = self.controller.get_selected_node()
        self.assertEqual(selected, root_node)
        self.assertTrue(root_node.is_selected)
        
        # 取消选择
        self.controller.select_node(None)
        selected = self.controller.get_selected_node()
        self.assertIsNone(selected)
        self.assertFalse(root_node.is_selected)
    
    def test_get_node_at_position(self):
        """测试根据位置获取节点"""
        root_node = self.controller.mindmap.get_root_node()
        
        # 在节点内的位置
        node = self.controller.get_node_at_position(root_node.x + 10, root_node.y + 10)
        self.assertEqual(node, root_node)
        
        # 在节点外的位置
        node = self.controller.get_node_at_position(0, 0)
        self.assertIsNone(node)
    
    def test_new_mindmap(self):
        """测试创建新思维导图"""
        # 先添加一些节点
        root_node = self.controller.mindmap.get_root_node()
        self.controller.add_child_node(root_node.id, "子节点")
        
        # 创建新思维导图
        self.controller.new_mindmap()
        
        # 验证重置
        new_root = self.controller.mindmap.get_root_node()
        self.assertIsNotNone(new_root)
        self.assertEqual(new_root.text, "中心主题")
        self.assertEqual(len(self.controller.mindmap.nodes), 1)


class TestFileManager(unittest.TestCase):
    """测试FileManager类"""
    
    def setUp(self):
        """测试前准备"""
        self.file_manager = FileManager()
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    def test_save_and_load_mindmap(self):
        """测试保存和加载思维导图"""
        # 创建测试思维导图
        mindmap = MindMap("测试思维导图")
        root_node = Node("根节点", 400, 300)
        root_id = mindmap.add_node(root_node)
        
        child_node = Node("子节点", 600, 300)
        child_id = mindmap.add_node(child_node, root_id)
        
        # 保存到临时文件
        file_path = os.path.join(self.temp_dir, "test.json")
        success = self.file_manager.save_mindmap(mindmap, file_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(file_path))
        
        # 加载文件
        loaded_mindmap = self.file_manager.load_mindmap(file_path)
        self.assertIsNotNone(loaded_mindmap)
        
        # 验证加载的数据
        self.assertEqual(loaded_mindmap.title, mindmap.title)
        self.assertEqual(loaded_mindmap.root_id, mindmap.root_id)
        self.assertEqual(len(loaded_mindmap.nodes), len(mindmap.nodes))
        
        # 验证节点关系
        loaded_root = loaded_mindmap.get_node(root_id)
        loaded_child = loaded_mindmap.get_node(child_id)
        self.assertIsNotNone(loaded_root)
        self.assertIsNotNone(loaded_child)
        self.assertEqual(loaded_child.parent_id, root_id)
        self.assertIn(child_id, loaded_root.children_ids)
    
    def test_create_new_mindmap(self):
        """测试创建新思维导图"""
        mindmap = self.file_manager.create_new_mindmap()
        self.assertIsNotNone(mindmap)
        self.assertEqual(mindmap.title, "新思维导图")
        self.assertEqual(len(mindmap.nodes), 0)
        self.assertFalse(self.file_manager.is_file_modified())
    
    def test_file_modification_tracking(self):
        """测试文件修改状态跟踪"""
        # 初始状态
        self.assertFalse(self.file_manager.is_file_modified())
        
        # 设置修改状态
        self.file_manager.set_modified(True)
        self.assertTrue(self.file_manager.is_file_modified())
        
        # 清除修改状态
        self.file_manager.set_modified(False)
        self.assertFalse(self.file_manager.is_file_modified())
    
    def test_export_to_text(self):
        """测试导出为文本"""
        # 创建测试思维导图
        mindmap = MindMap("测试导出")
        root_node = Node("项目管理", 400, 300)
        root_id = mindmap.add_node(root_node)
        
        child1 = Node("需求分析", 600, 250)
        child1_id = mindmap.add_node(child1, root_id)
        
        child2 = Node("设计开发", 600, 350)
        child2_id = mindmap.add_node(child2, root_id)
        
        grandchild = Node("用户调研", 800, 250)
        mindmap.add_node(grandchild, child1_id)
        
        # 导出到临时文件
        file_path = os.path.join(self.temp_dir, "export.txt")
        success = self.file_manager.export_to_text(mindmap, file_path)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(file_path))
        
        # 验证导出内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        self.assertIn("测试导出", content)
        self.assertIn("项目管理", content)
        self.assertIn("需求分析", content)
        self.assertIn("设计开发", content)
        self.assertIn("用户调研", content)
    
    def test_load_nonexistent_file(self):
        """测试加载不存在的文件"""
        nonexistent_path = os.path.join(self.temp_dir, "nonexistent.json")
        mindmap = self.file_manager.load_mindmap(nonexistent_path)
        self.assertIsNone(mindmap)


if __name__ == '__main__':
    unittest.main()
