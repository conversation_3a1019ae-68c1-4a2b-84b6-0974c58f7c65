"""
思维导图数据模型
"""
from typing import Dict, List, Optional, Any
from .node import Node


class MindMap:
    """思维导图类，管理所有节点和它们的关系"""
    
    def __init__(self, title: str = "新思维导图"):
        """
        初始化思维导图
        
        Args:
            title: 思维导图标题
        """
        self.title = title
        self.nodes: Dict[str, Node] = {}
        self.root_id: Optional[str] = None
        self.selected_node_id: Optional[str] = None
        
    def add_node(self, node: Node, parent_id: Optional[str] = None) -> str:
        """
        添加节点
        
        Args:
            node: 要添加的节点
            parent_id: 父节点ID，如果为None则作为根节点
            
        Returns:
            添加的节点ID
        """
        self.nodes[node.id] = node
        
        if parent_id is None:
            # 设置为根节点
            if self.root_id is None:
                self.root_id = node.id
                node.is_root = True
        else:
            # 设置父子关系
            if parent_id in self.nodes:
                parent_node = self.nodes[parent_id]
                parent_node.add_child(node.id)
                node.set_parent(parent_id)
        
        return node.id
    
    def remove_node(self, node_id: str) -> bool:
        """
        删除节点及其所有子节点
        
        Args:
            node_id: 要删除的节点ID
            
        Returns:
            是否成功删除
        """
        if node_id not in self.nodes:
            return False
        
        node = self.nodes[node_id]
        
        # 递归删除所有子节点
        for child_id in node.children_ids.copy():
            self.remove_node(child_id)
        
        # 从父节点中移除引用
        if node.parent_id and node.parent_id in self.nodes:
            parent_node = self.nodes[node.parent_id]
            parent_node.remove_child(node_id)
        
        # 如果是根节点，清除根节点引用
        if node_id == self.root_id:
            self.root_id = None
        
        # 如果是选中节点，清除选中状态
        if node_id == self.selected_node_id:
            self.selected_node_id = None
        
        # 删除节点
        del self.nodes[node_id]
        return True
    
    def get_node(self, node_id: str) -> Optional[Node]:
        """获取节点"""
        return self.nodes.get(node_id)
    
    def get_root_node(self) -> Optional[Node]:
        """获取根节点"""
        if self.root_id:
            return self.nodes.get(self.root_id)
        return None
    
    def get_children(self, node_id: str) -> List[Node]:
        """获取节点的所有子节点"""
        if node_id not in self.nodes:
            return []
        
        node = self.nodes[node_id]
        children = []
        for child_id in node.children_ids:
            if child_id in self.nodes:
                children.append(self.nodes[child_id])
        return children
    
    def get_parent(self, node_id: str) -> Optional[Node]:
        """获取节点的父节点"""
        if node_id not in self.nodes:
            return None
        
        node = self.nodes[node_id]
        if node.parent_id and node.parent_id in self.nodes:
            return self.nodes[node.parent_id]
        return None
    
    def select_node(self, node_id: Optional[str]) -> None:
        """选择节点"""
        # 清除之前的选择
        if self.selected_node_id and self.selected_node_id in self.nodes:
            self.nodes[self.selected_node_id].is_selected = False
        
        # 设置新的选择
        self.selected_node_id = node_id
        if node_id and node_id in self.nodes:
            self.nodes[node_id].is_selected = True
    
    def get_selected_node(self) -> Optional[Node]:
        """获取当前选中的节点"""
        if self.selected_node_id:
            return self.nodes.get(self.selected_node_id)
        return None
    
    def get_all_nodes(self) -> List[Node]:
        """获取所有节点"""
        return list(self.nodes.values())
    
    def clear(self) -> None:
        """清空思维导图"""
        self.nodes.clear()
        self.root_id = None
        self.selected_node_id = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'title': self.title,
            'root_id': self.root_id,
            'nodes': {node_id: node.to_dict() for node_id, node in self.nodes.items()}
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MindMap':
        """从字典创建思维导图"""
        mindmap = cls(data.get('title', '新思维导图'))
        mindmap.root_id = data.get('root_id')
        
        # 重建节点
        nodes_data = data.get('nodes', {})
        for node_id, node_data in nodes_data.items():
            node = Node.from_dict(node_data)
            mindmap.nodes[node_id] = node
        
        return mindmap
