"""
文件管理器 - 处理思维导图的保存和加载
"""
import json
import os
from typing import Optional
from ..models.mindmap import MindMap


class FileManager:
    """文件管理器类"""
    
    def __init__(self):
        self.current_file_path: Optional[str] = None
        self.is_modified = False
    
    def save_mindmap(self, mindmap: MindMap, file_path: str) -> bool:
        """
        保存思维导图到文件
        
        Args:
            mindmap: 要保存的思维导图
            file_path: 文件路径
            
        Returns:
            是否保存成功
        """
        try:
            # 确保目录存在
            directory = os.path.dirname(file_path)
            if directory and not os.path.exists(directory):
                os.makedirs(directory)
            
            # 转换为字典并保存
            data = mindmap.to_dict()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.current_file_path = file_path
            self.is_modified = False
            return True
            
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def load_mindmap(self, file_path: str) -> Optional[MindMap]:
        """
        从文件加载思维导图
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的思维导图，失败时返回None
        """
        try:
            if not os.path.exists(file_path):
                print(f"文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            mindmap = MindMap.from_dict(data)
            self.current_file_path = file_path
            self.is_modified = False
            return mindmap
            
        except Exception as e:
            print(f"加载文件失败: {e}")
            return None
    
    def create_new_mindmap(self) -> MindMap:
        """创建新的思维导图"""
        self.current_file_path = None
        self.is_modified = False
        return MindMap("新思维导图")
    
    def get_current_file_path(self) -> Optional[str]:
        """获取当前文件路径"""
        return self.current_file_path
    
    def get_current_file_name(self) -> str:
        """获取当前文件名"""
        if self.current_file_path:
            return os.path.basename(self.current_file_path)
        return "未命名"
    
    def set_modified(self, modified: bool = True) -> None:
        """设置修改状态"""
        self.is_modified = modified
    
    def is_file_modified(self) -> bool:
        """检查文件是否已修改"""
        return self.is_modified
    
    def can_close(self) -> bool:
        """检查是否可以关闭（没有未保存的修改）"""
        return not self.is_modified
    
    def export_to_text(self, mindmap: MindMap, file_path: str) -> bool:
        """
        导出思维导图为文本格式
        
        Args:
            mindmap: 要导出的思维导图
            file_path: 导出文件路径
            
        Returns:
            是否导出成功
        """
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"思维导图: {mindmap.title}\n")
                f.write("=" * 50 + "\n\n")
                
                # 递归写入节点结构
                root_node = mindmap.get_root_node()
                if root_node:
                    self._write_node_text(f, mindmap, root_node, 0)
            
            return True
            
        except Exception as e:
            print(f"导出文本失败: {e}")
            return False
    
    def _write_node_text(self, file, mindmap: MindMap, node, level: int) -> None:
        """递归写入节点文本"""
        indent = "  " * level
        file.write(f"{indent}- {node.text}\n")
        
        # 写入子节点
        children = mindmap.get_children(node.id)
        for child in children:
            self._write_node_text(file, mindmap, child, level + 1)
