{"title": "示例思维导图", "root_id": "root-001", "nodes": {"root-001": {"id": "root-001", "text": "项目管理", "x": 400, "y": 300, "width": 120, "height": 40, "parent_id": null, "children_ids": ["child-001", "child-002", "child-003"], "is_root": true}, "child-001": {"id": "child-001", "text": "需求分析", "x": 600, "y": 200, "width": 120, "height": 40, "parent_id": "root-001", "children_ids": ["grandchild-001", "grandchild-002"], "is_root": false}, "child-002": {"id": "child-002", "text": "设计开发", "x": 600, "y": 300, "width": 120, "height": 40, "parent_id": "root-001", "children_ids": ["grandchild-003"], "is_root": false}, "child-003": {"id": "child-003", "text": "测试部署", "x": 600, "y": 400, "width": 120, "height": 40, "parent_id": "root-001", "children_ids": [], "is_root": false}, "grandchild-001": {"id": "grandchild-001", "text": "用户调研", "x": 800, "y": 150, "width": 120, "height": 40, "parent_id": "child-001", "children_ids": [], "is_root": false}, "grandchild-002": {"id": "grandchild-002", "text": "功能规划", "x": 800, "y": 250, "width": 120, "height": 40, "parent_id": "child-001", "children_ids": [], "is_root": false}, "grandchild-003": {"id": "grandchild-003", "text": "架构设计", "x": 800, "y": 300, "width": 120, "height": 40, "parent_id": "child-002", "children_ids": [], "is_root": false}}}