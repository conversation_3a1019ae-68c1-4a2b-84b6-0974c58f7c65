"""
测试工具函数
"""
import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.utils.helpers import (
    distance, clamp, point_in_rect, rect_intersect, 
    get_connection_points, create_default_font
)
from src.models.node import Node


class TestHelpers(unittest.TestCase):
    """测试辅助函数"""
    
    def test_distance(self):
        """测试距离计算"""
        # 水平距离
        self.assertEqual(distance(0, 0, 3, 0), 3.0)
        
        # 垂直距离
        self.assertEqual(distance(0, 0, 0, 4), 4.0)
        
        # 对角距离
        self.assertEqual(distance(0, 0, 3, 4), 5.0)
        
        # 相同点
        self.assertEqual(distance(5, 5, 5, 5), 0.0)
    
    def test_clamp(self):
        """测试值限制"""
        # 在范围内
        self.assertEqual(clamp(5, 0, 10), 5)
        
        # 超出上限
        self.assertEqual(clamp(15, 0, 10), 10)
        
        # 低于下限
        self.assertEqual(clamp(-5, 0, 10), 0)
        
        # 边界值
        self.assertEqual(clamp(0, 0, 10), 0)
        self.assertEqual(clamp(10, 0, 10), 10)
    
    def test_point_in_rect(self):
        """测试点是否在矩形内"""
        # 矩形: x=10, y=20, width=100, height=50
        
        # 内部点
        self.assertTrue(point_in_rect(50, 40, 10, 20, 100, 50))
        
        # 外部点
        self.assertFalse(point_in_rect(5, 40, 10, 20, 100, 50))  # 左侧
        self.assertFalse(point_in_rect(120, 40, 10, 20, 100, 50))  # 右侧
        self.assertFalse(point_in_rect(50, 15, 10, 20, 100, 50))  # 上方
        self.assertFalse(point_in_rect(50, 75, 10, 20, 100, 50))  # 下方
        
        # 边界点
        self.assertTrue(point_in_rect(10, 20, 10, 20, 100, 50))  # 左上角
        self.assertTrue(point_in_rect(110, 70, 10, 20, 100, 50))  # 右下角
    
    def test_rect_intersect(self):
        """测试矩形相交"""
        # 相交的矩形
        self.assertTrue(rect_intersect(0, 0, 10, 10, 5, 5, 10, 10))
        
        # 不相交的矩形
        self.assertFalse(rect_intersect(0, 0, 10, 10, 20, 20, 10, 10))
        
        # 相邻但不相交
        self.assertFalse(rect_intersect(0, 0, 10, 10, 10, 0, 10, 10))
        
        # 完全包含
        self.assertTrue(rect_intersect(0, 0, 20, 20, 5, 5, 10, 10))
        
        # 相同矩形
        self.assertTrue(rect_intersect(0, 0, 10, 10, 0, 0, 10, 10))
    
    def test_get_connection_points(self):
        """测试获取连接点"""
        # 创建两个节点
        node1 = Node("节点1", 100, 100)  # 100x100, 120x40
        node2 = Node("节点2", 300, 100)  # 300x100, 120x40
        
        # 获取连接点
        p1, p2 = get_connection_points(node1, node2)
        
        # 验证连接点（水平连接）
        self.assertEqual(p1[0], node1.x + node1.width)  # 右边缘
        self.assertEqual(p2[0], node2.x)  # 左边缘
        
        # 测试垂直连接
        node3 = Node("节点3", 100, 300)  # 100x300, 120x40
        p1, p2 = get_connection_points(node1, node3)
        
        # 验证连接点（垂直连接）
        self.assertEqual(p1[1], node1.y + node1.height)  # 下边缘
        self.assertEqual(p2[1], node3.y)  # 上边缘
    
    def test_create_default_font(self):
        """测试创建默认字体"""
        # 注意：这个测试在没有GUI环境时可能会失败
        try:
            import wx
            app = wx.App(False)  # 创建临时应用
            
            font = create_default_font(14)
            self.assertIsNotNone(font)
            self.assertEqual(font.GetPointSize(), 14)
            
            app.Destroy()
        except ImportError:
            # 如果没有wx，跳过这个测试
            self.skipTest("wxPython not available")
        except Exception:
            # 如果没有显示环境，跳过这个测试
            self.skipTest("No display environment available")


class TestConstants(unittest.TestCase):
    """测试常量定义"""
    
    def test_constants_exist(self):
        """测试常量是否存在"""
        from src.utils.constants import (
            WINDOW_WIDTH, WINDOW_HEIGHT, WINDOW_TITLE,
            NODE_WIDTH, NODE_HEIGHT, NODE_MARGIN,
            ZOOM_FACTOR, MIN_ZOOM, MAX_ZOOM
        )
        
        # 验证窗口常量
        self.assertIsInstance(WINDOW_WIDTH, int)
        self.assertIsInstance(WINDOW_HEIGHT, int)
        self.assertIsInstance(WINDOW_TITLE, str)
        
        # 验证节点常量
        self.assertIsInstance(NODE_WIDTH, int)
        self.assertIsInstance(NODE_HEIGHT, int)
        self.assertIsInstance(NODE_MARGIN, int)
        
        # 验证缩放常量
        self.assertIsInstance(ZOOM_FACTOR, float)
        self.assertIsInstance(MIN_ZOOM, float)
        self.assertIsInstance(MAX_ZOOM, float)
        
        # 验证缩放范围合理性
        self.assertGreater(ZOOM_FACTOR, 1.0)
        self.assertGreater(MIN_ZOOM, 0.0)
        self.assertGreater(MAX_ZOOM, MIN_ZOOM)


if __name__ == '__main__':
    unittest.main()
