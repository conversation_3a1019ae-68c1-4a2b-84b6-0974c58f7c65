# 常量定义
import wx

# 窗口设置
WINDOW_WIDTH = 1200
WINDOW_HEIGHT = 800
WINDOW_TITLE = "思维导图"

# 节点设置
NODE_WIDTH = 120
NODE_HEIGHT = 40
NODE_MARGIN = 20
NODE_BORDER_WIDTH = 2

# 颜色设置
NODE_BG_COLOR = wx.Colour(255, 255, 255)
NODE_BORDER_COLOR = wx.Colour(100, 100, 100)
NODE_SELECTED_COLOR = wx.Colour(0, 120, 215)
NODE_TEXT_COLOR = wx.Colour(0, 0, 0)
CANVAS_BG_COLOR = wx.Colour(248, 248, 248)
LINE_COLOR = wx.Colour(150, 150, 150)

# 字体设置
NODE_FONT_SIZE = 12
NODE_FONT_FAMILY = wx.FONTFAMILY_DEFAULT

# 操作设置
ZOOM_FACTOR = 1.1
MIN_ZOOM = 0.5
MAX_ZOOM = 3.0
DRAG_THRESHOLD = 5

# 文件设置
DEFAULT_FILE_EXT = ".json"
FILE_WILDCARD = "思维导图文件 (*.json)|*.json|所有文件 (*.*)|*.*"
