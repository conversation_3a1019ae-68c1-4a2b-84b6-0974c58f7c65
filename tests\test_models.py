"""
测试数据模型
"""
import unittest
import sys
import os

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.models.node import Node
from src.models.mindmap import MindMap


class TestNode(unittest.TestCase):
    """测试Node类"""
    
    def setUp(self):
        """测试前准备"""
        self.node = Node("测试节点", 100, 200)
    
    def test_node_creation(self):
        """测试节点创建"""
        self.assertEqual(self.node.text, "测试节点")
        self.assertEqual(self.node.x, 100)
        self.assertEqual(self.node.y, 200)
        self.assertEqual(self.node.width, 120)
        self.assertEqual(self.node.height, 40)
        self.assertIsNone(self.node.parent_id)
        self.assertEqual(len(self.node.children_ids), 0)
        self.assertFalse(self.node.is_selected)
    
    def test_add_child(self):
        """测试添加子节点"""
        child_id = "child-001"
        self.node.add_child(child_id)
        self.assertIn(child_id, self.node.children_ids)
        
        # 重复添加应该不会增加
        self.node.add_child(child_id)
        self.assertEqual(self.node.children_ids.count(child_id), 1)
    
    def test_remove_child(self):
        """测试移除子节点"""
        child_id = "child-001"
        self.node.add_child(child_id)
        self.node.remove_child(child_id)
        self.assertNotIn(child_id, self.node.children_ids)
    
    def test_move_to(self):
        """测试移动节点"""
        new_x, new_y = 300, 400
        self.node.move_to(new_x, new_y)
        self.assertEqual(self.node.x, new_x)
        self.assertEqual(self.node.y, new_y)
    
    def test_contains_point(self):
        """测试点是否在节点内"""
        # 节点内的点
        self.assertTrue(self.node.contains_point(150, 220))
        # 节点外的点
        self.assertFalse(self.node.contains_point(50, 50))
        # 边界点
        self.assertTrue(self.node.contains_point(100, 200))  # 左上角
        self.assertTrue(self.node.contains_point(220, 240))  # 右下角
    
    def test_to_dict_and_from_dict(self):
        """测试序列化和反序列化"""
        # 设置一些属性
        self.node.add_child("child-001")
        self.node.set_parent("parent-001")
        
        # 转换为字典
        data = self.node.to_dict()
        
        # 从字典创建新节点
        new_node = Node.from_dict(data)
        
        # 验证属性
        self.assertEqual(new_node.text, self.node.text)
        self.assertEqual(new_node.x, self.node.x)
        self.assertEqual(new_node.y, self.node.y)
        self.assertEqual(new_node.parent_id, self.node.parent_id)
        self.assertEqual(new_node.children_ids, self.node.children_ids)


class TestMindMap(unittest.TestCase):
    """测试MindMap类"""
    
    def setUp(self):
        """测试前准备"""
        self.mindmap = MindMap("测试思维导图")
    
    def test_mindmap_creation(self):
        """测试思维导图创建"""
        self.assertEqual(self.mindmap.title, "测试思维导图")
        self.assertEqual(len(self.mindmap.nodes), 0)
        self.assertIsNone(self.mindmap.root_id)
        self.assertIsNone(self.mindmap.selected_node_id)
    
    def test_add_root_node(self):
        """测试添加根节点"""
        root_node = Node("根节点", 400, 300)
        node_id = self.mindmap.add_node(root_node)
        
        self.assertEqual(self.mindmap.root_id, node_id)
        self.assertIn(node_id, self.mindmap.nodes)
        self.assertTrue(self.mindmap.nodes[node_id].is_root)
    
    def test_add_child_node(self):
        """测试添加子节点"""
        # 先添加根节点
        root_node = Node("根节点", 400, 300)
        root_id = self.mindmap.add_node(root_node)
        
        # 添加子节点
        child_node = Node("子节点", 600, 300)
        child_id = self.mindmap.add_node(child_node, root_id)
        
        # 验证关系
        self.assertEqual(child_node.parent_id, root_id)
        self.assertIn(child_id, root_node.children_ids)
    
    def test_remove_node(self):
        """测试删除节点"""
        # 创建节点结构
        root_node = Node("根节点", 400, 300)
        root_id = self.mindmap.add_node(root_node)
        
        child_node = Node("子节点", 600, 300)
        child_id = self.mindmap.add_node(child_node, root_id)
        
        grandchild_node = Node("孙节点", 800, 300)
        grandchild_id = self.mindmap.add_node(grandchild_node, child_id)
        
        # 删除子节点（应该同时删除孙节点）
        success = self.mindmap.remove_node(child_id)
        
        self.assertTrue(success)
        self.assertNotIn(child_id, self.mindmap.nodes)
        self.assertNotIn(grandchild_id, self.mindmap.nodes)
        self.assertNotIn(child_id, root_node.children_ids)
    
    def test_select_node(self):
        """测试选择节点"""
        root_node = Node("根节点", 400, 300)
        root_id = self.mindmap.add_node(root_node)
        
        # 选择节点
        self.mindmap.select_node(root_id)
        self.assertEqual(self.mindmap.selected_node_id, root_id)
        self.assertTrue(root_node.is_selected)
        
        # 取消选择
        self.mindmap.select_node(None)
        self.assertIsNone(self.mindmap.selected_node_id)
        self.assertFalse(root_node.is_selected)
    
    def test_get_children(self):
        """测试获取子节点"""
        root_node = Node("根节点", 400, 300)
        root_id = self.mindmap.add_node(root_node)
        
        child1 = Node("子节点1", 600, 250)
        child1_id = self.mindmap.add_node(child1, root_id)
        
        child2 = Node("子节点2", 600, 350)
        child2_id = self.mindmap.add_node(child2, root_id)
        
        children = self.mindmap.get_children(root_id)
        self.assertEqual(len(children), 2)
        child_ids = [child.id for child in children]
        self.assertIn(child1_id, child_ids)
        self.assertIn(child2_id, child_ids)
    
    def test_to_dict_and_from_dict(self):
        """测试序列化和反序列化"""
        # 创建思维导图结构
        root_node = Node("根节点", 400, 300)
        root_id = self.mindmap.add_node(root_node)
        
        child_node = Node("子节点", 600, 300)
        child_id = self.mindmap.add_node(child_node, root_id)
        
        # 转换为字典
        data = self.mindmap.to_dict()
        
        # 从字典创建新思维导图
        new_mindmap = MindMap.from_dict(data)
        
        # 验证
        self.assertEqual(new_mindmap.title, self.mindmap.title)
        self.assertEqual(new_mindmap.root_id, self.mindmap.root_id)
        self.assertEqual(len(new_mindmap.nodes), len(self.mindmap.nodes))
        
        # 验证节点关系
        new_root = new_mindmap.get_node(root_id)
        new_child = new_mindmap.get_node(child_id)
        self.assertIsNotNone(new_root)
        self.assertIsNotNone(new_child)
        self.assertEqual(new_child.parent_id, root_id)
        self.assertIn(child_id, new_root.children_ids)


if __name__ == '__main__':
    unittest.main()
